/**
 * @file wifi_onenet.c
 * @brief ESP32 WiFi连接和OneNET云平台通信模块实现文件
 * @version 1.0
 * @date 2025-08-18
 *
 * @details
 * 本文件实现了ESP32连接WiFi网络并与中国移动OneNET云平台进行MQTT通信的所有功能。
 *
 * 主要功能模块：
 * 1. WiFi网络连接管理 - 自动连接、重连、状态监控
 * 2. OneNET MQTT通信 - 连接、订阅、发布、断线重连
 * 3. 数据上报服务 - 定时上报、手动上报、数据格式化
 * 4. 命令处理服务 - 接收云端命令、解析、回复
 * 5. 状态监控服务 - 连接状态、错误处理、日志记录
 *
 * @note 使用前请在wifi_onenet.h中配置正确的WiFi和OneNET参数
 * <AUTHOR>
 */

#include "wifi_onenet.h"

/* ==================== 日志标签定义 ==================== */
static const char *TAG = "WIFI_ONENET";

/* ==================== WiFi相关全局变量 ==================== */
/**
 * @brief WiFi连接重试计数器
 * @details 记录当前WiFi连接重试次数，达到最大次数后停止重试
 */
static int s_retry_num = 0;

/**
 * @brief WiFi连接状态
 * @details 记录当前WiFi的连接状态
 */
static wifi_state_t s_wifi_state = WIFI_STATE_DISCONNECTED;

/**
 * @brief WiFi状态变化回调函数指针
 * @details 当WiFi连接状态发生变化时，会调用此回调函数通知应用层
 */
static wifi_status_callback_t s_wifi_callback = NULL;

/**
 * @brief WiFi连接状态变化标志
 * @details 用于检测WiFi状态是否发生变化，避免重复调用回调函数
 */
static bool s_wifi_state_changed = false;

/* ==================== MQTT相关全局变量 ==================== */
/**
 * @brief MQTT客户端句柄
 * @details ESP-IDF MQTT客户端实例，用于与OneNET平台通信
 */
static esp_mqtt_client_handle_t s_mqtt_client = NULL;

/**
 * @brief MQTT客户端连接状态
 * @details 记录当前MQTT客户端的连接状态
 */
static mqtt_client_state_t s_mqtt_state = MQTT_STATE_UNKNOWN;

/**
 * @brief OneNET命令接收回调函数指针
 * @details 当接收到OneNET平台下发的命令时，会调用此回调函数
 */
static onenet_cmd_callback_t s_cmd_callback = NULL;

/* ==================== 自动上报相关变量 ==================== */
/**
 * @brief 自动上报间隔时间（毫秒）
 * @details 0表示禁用自动上报
 */
static uint32_t s_auto_report_interval = 0;

/**
 * @brief 上次上报时间戳
 * @details 用于计算是否到达上报时间
 */
static int64_t s_last_report_time = 0;

/**
 * @brief 当前ADC传感器数据
 * @details 存储最新的ADC电压值，由外部更新
 */
static float s_sensor_adc = 0.0f;

/* ==================== WIFI定位相关变量 ==================== */
/**
 * @brief WIFI定位自动上报间隔时间（毫秒）
 * @details 0表示禁用自动上报
 */
static uint32_t s_wifi_location_report_interval = 0;

/**
 * @brief WIFI定位上次上报时间戳
 * @details 用于计算是否到达上报时间
 */
static int64_t s_last_wifi_location_report_time = 0;

/**
 * @brief WIFI扫描结果缓存
 * @details 存储最近一次扫描的结果
 */
static wifi_ap_info_t s_scan_results[WIFI_SCAN_MAX_AP];

/**
 * @brief 扫描到的热点数量
 */
static int s_scan_count = 0;

/* ==================== 内部函数声明 ==================== */
/**
 * @brief WiFi事件处理函数（内部函数）
 * @param arg 用户参数（未使用）
 * @param event_base 事件基础类型
 * @param event_id 事件ID
 * @param event_data 事件数据
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

/**
 * @brief MQTT事件处理函数（内部函数）
 * @param handler_args 处理器参数
 * @param base 事件基础类型
 * @param event_id 事件ID
 * @param event_data 事件数据
 */
static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data);

/**
 * @brief 错误日志记录函数（内部函数）
 * @param message 错误消息描述
 * @param error_code 错误代码
 */
static void log_error_if_nonzero(const char *message, int error_code);

/**
 * @brief 处理自动数据上报（内部函数）
 */
static void process_auto_report(void);

/**
 * @brief 处理WIFI定位自动上报（内部函数）
 */
static void process_wifi_location_auto_report(void);

/**
 * @brief 处理WiFi状态变化（内部函数）
 */
static void process_wifi_state_change(void);

/* ==================== WiFi事件处理函数实现 ==================== */
/**
 * @brief WiFi事件处理函数（裸机版本）
 * @param arg 用户参数（未使用）
 * @param event_base 事件基础类型（WIFI_EVENT或IP_EVENT）
 * @param event_id 具体的事件ID
 * @param event_data 事件相关数据
 *
 * @details
 * 此函数处理所有WiFi相关事件，使用状态机方式管理连接状态。
 * 不使用FreeRTOS事件组，而是直接更新状态变量。
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        /* WiFi STA模式启动，开始连接AP */
        ESP_LOGI(TAG, "WiFi STA started, connecting to AP...");
        s_wifi_state = WIFI_STATE_CONNECTING;
        s_wifi_state_changed = true;
        esp_wifi_connect();

    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        /* WiFi连接断开，执行重连逻辑 */
        if (s_retry_num < WIFI_MAXIMUM_RETRY) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "Retry to connect to the AP (attempt %d/%d)", s_retry_num, WIFI_MAXIMUM_RETRY);
            s_wifi_state = WIFI_STATE_CONNECTING;
        } else {
            /* 达到最大重试次数，设置失败状态 */
            ESP_LOGE(TAG, "Failed to connect to AP after %d attempts", WIFI_MAXIMUM_RETRY);
            s_wifi_state = WIFI_STATE_FAILED;
        }

        /* 标记状态已变化 */
        s_wifi_state_changed = true;
        ESP_LOGW(TAG, "WiFi disconnected");

    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        /* 获取到IP地址，连接成功 */
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "WiFi connected successfully! Got IP: " IPSTR, IP2STR(&event->ip_info.ip));

        /* 重置重试计数器 */
        s_retry_num = 0;

        /* 更新连接状态 */
        s_wifi_state = WIFI_STATE_CONNECTED;
        s_wifi_state_changed = true;
    }
}

/* ==================== MQTT事件处理函数实现 ==================== */
/**
 * @brief MQTT事件处理函数
 * @param handler_args 处理器参数（MQTT客户端句柄）
 * @param base 事件基础类型
 * @param event_id MQTT事件ID
 * @param event_data 事件数据
 *
 * @details
 * 此函数处理所有MQTT相关事件，包括：
 * 1. MQTT_EVENT_CONNECTED - MQTT连接成功，订阅相关主题
 * 2. MQTT_EVENT_DISCONNECTED - MQTT连接断开
 * 3. MQTT_EVENT_DATA - 接收到MQTT数据，处理OneNET命令
 * 4. MQTT_EVENT_ERROR - MQTT连接错误
 * 5. 其他MQTT事件的处理
 */
static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    ESP_LOGD(TAG, "Event dispatched from event loop base=%s, event_id=%" PRIi32 "", base, event_id);
    esp_mqtt_event_handle_t event = event_data;
    esp_mqtt_client_handle_t client = event->client;
    int msg_id;

    switch ((esp_mqtt_event_id_t)event_id) {
    case MQTT_EVENT_CONNECTED:
        /* MQTT连接成功 */
        ESP_LOGI(TAG, "MQTT connected to OneNET platform successfully!");
        s_mqtt_state = MQTT_STATE_CONNECTED;

        /* 订阅OneNET属性设置主题，用于接收云端下发的命令 */
        msg_id = esp_mqtt_client_subscribe(client, ONENET_TOPIC_PROP_SET, 0);
        ESP_LOGI(TAG, "Subscribed to property set topic: %s (msg_id=%d)", ONENET_TOPIC_PROP_SET, msg_id);
        break;

    case MQTT_EVENT_DISCONNECTED:
        /* MQTT连接断开 */
        ESP_LOGW(TAG, "MQTT disconnected from OneNET platform");
        s_mqtt_state = MQTT_STATE_DISCONNECTED;
        break;

    case MQTT_EVENT_SUBSCRIBED:
        /* MQTT主题订阅成功 */
        ESP_LOGI(TAG, "MQTT topic subscribed successfully, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_UNSUBSCRIBED:
        /* MQTT主题取消订阅 */
        ESP_LOGI(TAG, "MQTT topic unsubscribed, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_PUBLISHED:
        /* MQTT消息发布成功 */
        ESP_LOGD(TAG, "MQTT message published successfully, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_DATA:
        /* 接收到MQTT数据 */
        ESP_LOGI(TAG, "MQTT data received from OneNET platform");
        ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
        ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);

        /* 处理OneNET属性设置请求 */
        if (strncmp(event->topic, ONENET_TOPIC_PROP_SET, event->topic_len) == 0) {
            ESP_LOGI(TAG, "Received property set command from OneNET");

            /* 调用用户注册的命令处理回调函数 */
            if (s_cmd_callback) {
                s_cmd_callback(event->topic, event->data, event->data_len);
            }

            /* 构造回复消息，告知OneNET平台命令处理结果 */
            char reply[256];
            snprintf(reply, sizeof(reply),
                "{\"id\":\"1\",\"version\":\"1.0\",\"code\":200,\"method\":\"thing.service.property.set\",\"data\":{}}");

            /* 发布回复消息到OneNET平台 */
            msg_id = esp_mqtt_client_publish(client, ONENET_TOPIC_PROP_SET_REPLY, reply, strlen(reply), 1, 0);
            ESP_LOGI(TAG, "Sent reply to OneNET property set command, msg_id=%d", msg_id);
        }
        break;

    case MQTT_EVENT_ERROR:
        /* MQTT连接错误 */
        ESP_LOGE(TAG, "MQTT connection error occurred");
        s_mqtt_state = MQTT_STATE_ERROR;

        /* 详细错误信息记录 */
        if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
            log_error_if_nonzero("reported from esp-tls", event->error_handle->esp_tls_last_esp_err);
            log_error_if_nonzero("reported from tls stack", event->error_handle->esp_tls_stack_err);
            log_error_if_nonzero("captured as transport's socket errno",  event->error_handle->esp_transport_sock_errno);
            ESP_LOGE(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
        }
        break;

    default:
        /* 其他MQTT事件 */
        ESP_LOGD(TAG, "Other MQTT event id: %d", event->event_id);
        break;
    }
}

/* ==================== 辅助函数实现 ==================== */
/**
 * @brief 错误日志记录函数
 * @param message 错误消息描述
 * @param error_code 错误代码
 *
 * @details
 * 当错误代码不为0时，记录错误日志。用于MQTT连接错误的详细诊断。
 */
static void log_error_if_nonzero(const char *message, int error_code)
{
    if (error_code != 0) {
        ESP_LOGE(TAG, "Last error %s: 0x%x", message, error_code);
    }
}

/* ==================== WiFi功能函数实现 ==================== */
/**
 * @brief WiFi网络初始化（裸机版本）
 * @param callback WiFi状态变化回调函数
 * @return esp_err_t ESP_OK表示成功，ESP_FAIL表示失败
 *
 * @details
 * 此函数完成WiFi网络的完整初始化过程（裸机版本）：
 * 1. 初始化网络接口（netif）
 * 2. 创建默认事件循环
 * 3. 创建默认WiFi STA接口
 * 4. 初始化WiFi驱动
 * 5. 注册WiFi和IP事件处理函数
 * 6. 配置WiFi连接参数（SSID、密码、认证模式等）
 * 7. 设置WiFi为STA模式并启动
 *
 * @note 调用此函数前需要先调用nvs_flash_init()初始化NVS存储
 * @warning 请确保在wifi_onenet.h中正确配置了WIFI_SSID和WIFI_PASSWORD
 */
esp_err_t wifi_init(wifi_status_callback_t callback)
{
    /* 保存用户回调函数 */
    s_wifi_callback = callback;

    /* 初始化状态变量 */
    s_wifi_state = WIFI_STATE_DISCONNECTED;
    s_wifi_state_changed = false;
    s_retry_num = 0;

    /* 初始化网络接口 */
    ESP_ERROR_CHECK(esp_netif_init());

    /* 创建默认事件循环 */
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    /* 创建默认WiFi STA网络接口 */
    esp_netif_create_default_wifi_sta();

    /* 初始化WiFi驱动，使用默认配置 */
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    /* 注册WiFi事件处理函数 */
    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;

    /* 注册所有WiFi事件的处理函数 */
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));

    /* 注册IP获取事件的处理函数 */
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    /* 配置WiFi连接参数 */
    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,                      /* WiFi网络名称 */
            .password = WIFI_PASSWORD,              /* WiFi密码 */
            .threshold.authmode = WIFI_AUTH_WPA2_PSK, /* 认证模式：WPA2-PSK */
            .pmf_cfg = {
                .capable = true,                    /* 支持PMF（Protected Management Frames） */
                .required = false                   /* PMF不是必需的 */
            },
        },
    };

    /* 设置WiFi工作模式为STA（Station）模式 */
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));

    /* 设置WiFi配置参数 */
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));

    /* 启动WiFi服务 */
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "WiFi initialization completed successfully (bare metal version)");
    ESP_LOGI(TAG, "Configured to connect to SSID: %s", WIFI_SSID);

    return ESP_OK;
}

/**
 * @brief 启动WiFi连接（裸机版本，非阻塞）
 * @return esp_err_t ESP_OK表示启动成功，ESP_FAIL表示启动失败
 *
 * @details
 * 此函数启动WiFi连接过程，但不会阻塞等待结果。
 * 连接状态需要通过wifi_get_state()函数查询或通过回调函数获取。
 *
 * @note 调用此函数前必须先调用wifi_init()进行初始化
 * @note 连接结果通过状态查询或回调函数获取，不会阻塞
 */
esp_err_t wifi_connect(void)
{
    ESP_LOGI(TAG, "Starting WiFi connection (non-blocking)...");

    /* 重置状态 */
    s_retry_num = 0;
    s_wifi_state = WIFI_STATE_CONNECTING;
    s_wifi_state_changed = true;

    /* 启动WiFi连接（非阻塞） */
    esp_err_t ret = esp_wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi connection");
        s_wifi_state = WIFI_STATE_FAILED;
        s_wifi_state_changed = true;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "WiFi connection started, use wifi_get_state() to check status");
    return ESP_OK;
}

/**
 * @brief 断开WiFi网络连接
 * @return esp_err_t ESP_OK表示断开成功，其他值表示断开失败
 *
 * @details 主动断开当前的WiFi连接
 */
esp_err_t wifi_disconnect(void)
{
    ESP_LOGI(TAG, "Disconnecting from WiFi...");
    return esp_wifi_disconnect();
}

/**
 * @brief 获取WiFi连接状态（裸机版本）
 * @return true WiFi已连接，false WiFi未连接
 *
 * @details 返回当前WiFi的连接状态，可用于判断网络是否可用
 */
bool wifi_is_connected(void)
{
    return (s_wifi_state == WIFI_STATE_CONNECTED);
}

/**
 * @brief 获取WiFi状态（详细状态）
 * @return wifi_state_t 当前WiFi状态
 *
 * @details 返回详细的WiFi连接状态
 */
wifi_state_t wifi_get_state(void)
{
    return s_wifi_state;
}

/* ==================== OneNET MQTT功能函数实现 ==================== */
/**
 * @brief OneNET MQTT客户端初始化
 * @param cmd_callback 命令接收回调函数
 * @return esp_err_t ESP_OK表示成功，ESP_FAIL表示失败
 *
 * @details
 * 此函数完成OneNET MQTT客户端的初始化：
 * 1. 保存用户命令回调函数
 * 2. 配置MQTT连接参数（服务器地址、端口、认证信息）
 * 3. 创建MQTT客户端实例
 * 4. 注册MQTT事件处理函数
 * 5. 设置MQTT客户端状态为已初始化
 *
 * MQTT连接参数说明：
 * - 传输协议：TCP
 * - 服务器地址：OneNET官方MQTT服务器
 * - 端口：1883（标准MQTT端口）
 * - 客户端ID：设备ID
 * - 用户名：产品ID
 * - 密码：设备Token（OneNET平台生成的认证凭证）
 *
 * @note 调用此函数前请确保已正确配置OneNET相关参数
 * @warning 请确保在wifi_onenet.h中正确配置了OneNET平台参数
 */
esp_err_t onenet_mqtt_init(onenet_cmd_callback_t cmd_callback)
{
    /* 保存用户命令回调函数 */
    s_cmd_callback = cmd_callback;

    ESP_LOGI(TAG, "Initializing OneNET MQTT client...");
    ESP_LOGI(TAG, "Product ID: %s", ONENET_PRODUCT_ID);
    ESP_LOGI(TAG, "Device ID: %s", ONENET_DEVICE_ID);
    ESP_LOGI(TAG, "MQTT Host: %s:%d", ONENET_MQTT_HOST, ONENET_MQTT_PORT);

    /* 配置MQTT连接参数 */
    esp_mqtt_client_config_t mqtt_cfg = {
        .broker.address.transport = MQTT_TRANSPORT_OVER_TCP,    /* 使用TCP传输 */
        .broker.address.hostname = ONENET_MQTT_HOST,            /* OneNET MQTT服务器地址 */
        .broker.address.port = ONENET_MQTT_PORT,                /* OneNET MQTT服务器端口 */
        .credentials.client_id = ONENET_DEVICE_ID,              /* 客户端ID（设备ID） */
        .credentials.username = ONENET_PRODUCT_ID,              /* 用户名（产品ID） */
        .credentials.authentication.password = ONENET_DEVICE_TOKEN, /* 密码（设备Token） */
    };

    /* 创建MQTT客户端实例 */
    s_mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
    if (s_mqtt_client == NULL) {
        ESP_LOGE(TAG, "Failed to initialize MQTT client");
        return ESP_FAIL;
    }

    /* 注册MQTT事件处理函数 */
    esp_err_t ret = esp_mqtt_client_register_event(s_mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, s_mqtt_client);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register MQTT event handler");
        return ESP_FAIL;
    }

    /* 设置MQTT客户端状态为已初始化 */
    s_mqtt_state = MQTT_STATE_INIT;
    ESP_LOGI(TAG, "OneNET MQTT client initialized successfully");

    return ESP_OK;
}

/**
 * @brief 启动OneNET MQTT连接
 * @return esp_err_t ESP_OK表示启动成功，ESP_FAIL表示启动失败
 *
 * @details
 * 启动MQTT客户端，开始连接OneNET平台。连接成功后会自动订阅相关主题。
 *
 * 连接过程：
 * 1. 启动MQTT客户端
 * 2. 客户端自动连接到OneNET平台
 * 3. 连接成功后触发MQTT_EVENT_CONNECTED事件
 * 4. 在事件处理函数中自动订阅OneNET命令主题
 *
 * @note 调用此函数前必须先调用onenet_mqtt_init()进行初始化
 * @note 建议在WiFi连接成功后再调用此函数
 */
esp_err_t onenet_mqtt_start(void)
{
    if (s_mqtt_client == NULL) {
        ESP_LOGE(TAG, "MQTT client not initialized, please call onenet_mqtt_init() first");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Starting OneNET MQTT client...");

    /* 启动MQTT客户端 */
    esp_err_t ret = esp_mqtt_client_start(s_mqtt_client);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "OneNET MQTT client started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start OneNET MQTT client");
    }

    return ret;
}

/**
 * @brief 停止OneNET MQTT连接
 * @return esp_err_t ESP_OK表示停止成功，ESP_FAIL表示停止失败
 *
 * @details 断开与OneNET平台的MQTT连接，停止所有MQTT相关服务
 */
esp_err_t onenet_mqtt_stop(void)
{
    if (s_mqtt_client == NULL) {
        ESP_LOGE(TAG, "MQTT client not initialized");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Stopping OneNET MQTT client...");

    /* 停止MQTT客户端 */
    esp_err_t ret = esp_mqtt_client_stop(s_mqtt_client);
    if (ret == ESP_OK) {
        s_mqtt_state = MQTT_STATE_DISCONNECTED;
        ESP_LOGI(TAG, "OneNET MQTT client stopped successfully");
    } else {
        ESP_LOGE(TAG, "Failed to stop OneNET MQTT client");
    }

    return ret;
}

/**
 * @brief 获取MQTT客户端连接状态
 * @return mqtt_client_state_t 当前MQTT客户端状态
 *
 * @details 返回当前MQTT客户端的连接状态，用于判断是否可以进行数据上报
 */
mqtt_client_state_t onenet_mqtt_get_state(void)
{
    return s_mqtt_state;
}

/* ==================== 数据上报功能函数实现 ==================== */
/**
 * @brief 上报单个设备属性到OneNET平台
 * @param property_name 属性名称，必须与OneNET平台物模型中定义的属性名一致
 * @param value 属性值，以字符串形式传入
 * @return int 消息ID，成功时返回正数，失败时返回-1
 *
 * @details
 * 向OneNET平台上报单个设备属性数据。函数会自动构造符合OneNET物模型规范的JSON消息。
 *
 * 消息格式：
 * {
 *   "id": "随机消息ID",
 *   "version": "1.0",
 *   "params": {
 *     "属性名": {
 *       "value": "属性值"
 *     }
 *   }
 * }
 *
 * @note 调用此函数前请确保MQTT已连接到OneNET平台
 * @warning 属性名称必须与OneNET平台物模型中定义的属性名完全一致
 */
int onenet_report_property(const char* property_name, const char* value)
{
    /* 参数有效性检查 */
    if (s_mqtt_client == NULL || s_mqtt_state != MQTT_STATE_CONNECTED) {
        ESP_LOGW(TAG, "MQTT client not connected, cannot report property");
        return -1;
    }

    if (property_name == NULL || value == NULL) {
        ESP_LOGE(TAG, "Invalid parameters: property_name or value is NULL");
        return -1;
    }

    ESP_LOGD(TAG, "Reporting single property: %s = %s", property_name, value);

    /* 构造属性内容（不包含外层params） */
    char params_content[256];
    snprintf(params_content, sizeof(params_content),
        "\"%s\":{\"value\":\"%s\"}",
        property_name, value);

    /* 调用多属性上报函数 */
    return onenet_report_properties(params_content);
}

/**
 * @brief 上报多个设备属性到OneNET平台
 * @param params_content 参数内容字符串（不包含外层的params结构）
 * @return int 消息ID，成功时返回正数，失败时返回-1
 *
 * @details
 * 向OneNET平台上报多个设备属性数据。参数内容会被自动包装成完整的OneNET格式。
 *
 * 完整消息格式：
 * {
 *   "id": "随机消息ID",
 *   "params": {
 *     // 用户传入的params_content内容
 *   }
 * }
 *
 * @note 调用此函数前请确保MQTT已连接到OneNET平台
 * @warning 参数内容必须是有效的JSON属性格式
 */
int onenet_report_properties(const char* params_content)
{
    /* 参数有效性检查 */
    if (s_mqtt_client == NULL || s_mqtt_state != MQTT_STATE_CONNECTED) {
        ESP_LOGW(TAG, "MQTT client not connected, cannot report properties");
        return -1;
    }

    if (params_content == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: params_content is NULL");
        return -1;
    }

    ESP_LOGD(TAG, "Reporting properties to OneNET: %s", params_content);

    /* 生成随机消息ID */
    char message_id[15];
    snprintf(message_id, sizeof(message_id), "%ld", esp_random() % 1000000);

    /* 构造完整的OneNET消息，按照标准格式 */
    char onenet_msg[1024];
    snprintf(onenet_msg, sizeof(onenet_msg),
        "{\"id\":\"%s\",\"params\":{%s}}",
        message_id, params_content);

    /* 发布消息到OneNET平台 */
    int msg_id = esp_mqtt_client_publish(
        s_mqtt_client,                  /* MQTT客户端句柄 */
        ONENET_TOPIC_PROP_POST,         /* 发布主题 */
        onenet_msg,                     /* 消息内容 */
        strlen(onenet_msg),             /* 消息长度 */
        1,                              /* QoS等级：1（至少一次传输） */
        0                               /* Retain标志：0（不保留） */
    );

    /* 记录发布结果 */
    if (msg_id == -1) {
        ESP_LOGE(TAG, "Failed to publish properties to OneNET");
    }

    return msg_id;
}

/**
 * @brief 上报ADC数据到OneNET平台
 * @param adc_value ADC电压值（浮点数）
 * @return int 消息ID，成功时返回正数，失败时返回-1
 *
 * @details
 * 这是一个便捷函数，专门用于上报ADC数据。会自动格式化为保留一位小数的字符串。
 */
int onenet_report_adc(float adc_value)
{
    /* 参数有效性检查 */
    if (s_mqtt_client == NULL || s_mqtt_state != MQTT_STATE_CONNECTED) {
        ESP_LOGW(TAG, "MQTT client not connected, cannot report ADC data");
        return -1;
    }

    ESP_LOGD(TAG, "Reporting ADC data: %.1fV", adc_value);

    /* 构造ADC数据内容（不包含外层params） */
    char params_content[128];
    snprintf(params_content, sizeof(params_content),
        "\"ADC\":{\"value\":%.1f}",
        adc_value);

    /* 调用通用上报函数 */
    return onenet_report_properties(params_content);
}

/**
 * @brief 更新当前ADC数据值
 * @param adc_value 当前ADC电压值（浮点数）
 *
 * @details
 * 更新内部存储的ADC数据值，用于自动上报。
 * 应该在主循环中定期调用此函数来更新最新的ADC采集值。
 */
void onenet_update_adc_data(float adc_value)
{
    s_sensor_adc = adc_value;
    ESP_LOGD(TAG, "ADC data updated: %.1fV", adc_value);
}

/**
 * @brief 构造OneNET数据包（类似OneNet_FillBuf函数）
 * @param buf 输出缓冲区，用于存储构造的数据包
 * @param buf_size 缓冲区大小
 * @return int 构造的数据包长度，失败时返回-1
 *
 * @details
 * ，用于构造复杂的OneNET数据包。
 * 模拟您提供的OneNet_FillBuf函数的功能。
 */
int onenet_fill_buf(char *buf, size_t buf_size)
{
    if (buf == NULL || buf_size == 0) {
        ESP_LOGE(TAG, "Invalid buffer parameters");
        return -1;
    }

    char text[256];

    /* 清空缓冲区 */
    memset(buf, 0, buf_size);

    /* 开始构造JSON消息 */
    strcpy(buf, "{\"id\":\"123\",\"params\":{");

    /* 添加ADC数据 */
    memset(text, 0, sizeof(text));
    sprintf(text, "\"ADC\":{\"value\":%.1f}", s_sensor_adc);
    strcat(buf, text);


    /* 结束JSON */
    strcat(buf, "}}");

    return strlen(buf);
}

/* ==================== 裸机版本自动上报实现 ==================== */
/**
 * @brief 设置自动数据上报间隔（裸机版本）
 * @param report_interval_ms 数据上报间隔时间（毫秒），0表示禁用自动上报
 * @return esp_err_t ESP_OK表示设置成功
 *
 * @details
 * 设置自动数据上报的时间间隔。需要在主循环中定期调用onenet_process()函数来处理上报。
 */
esp_err_t onenet_set_auto_report_interval(uint32_t report_interval_ms)
{
    s_auto_report_interval = report_interval_ms;
    s_last_report_time = esp_timer_get_time() / 1000;  /* 重置上次上报时间 */

    if (report_interval_ms > 0) {
        ESP_LOGI(TAG, "Auto report interval set to %ld ms", report_interval_ms);
    } else {
        ESP_LOGI(TAG, "Auto report disabled");
    }

    return ESP_OK;
}

/**
 * @brief 处理自动数据上报（内部函数）
 * @details 检查是否到达上报时间，如果是则执行自动上报
 */
static void process_auto_report(void)
{
    /* 检查是否启用自动上报 */
    if (s_auto_report_interval == 0) {
        return;
    }

    /* 检查MQTT连接状态 */
    if (s_mqtt_state != MQTT_STATE_CONNECTED) {
        return;
    }

    /* 检查是否到达上报时间 */
    int64_t current_time = esp_timer_get_time() / 1000;  /* 当前时间（毫秒） */
    if ((current_time - s_last_report_time) >= s_auto_report_interval) {
        /* 使用当前ADC传感器数据（由外部更新） */
        /* Construct ADC data content (upload ADC data value only) */
        char params_content[128];
        snprintf(params_content, sizeof(params_content),
            "\"ADC\":{\"value\":%.1f}",
            s_sensor_adc);

        /* 上报数据到OneNET平台 */
        int msg_id = onenet_report_properties(params_content);
        if (msg_id == -1) {
            ESP_LOGW(TAG, "Auto report failed - MQTT publish error");
        }

        /* 更新上次上报时间 */
        s_last_report_time = current_time;
    }
}

/**
 * @brief 处理WIFI定位自动上报（内部函数）
 * @details 检查是否到达WIFI定位上报时间，如果是则执行自动上报
 */
static void process_wifi_location_auto_report(void)
{
    /* 检查是否启用WIFI定位自动上报 */
    if (s_wifi_location_report_interval == 0) {
        return;
    }

    /* 检查MQTT连接状态 */
    if (s_mqtt_state != MQTT_STATE_CONNECTED) {
        return;
    }

    /* 检查WiFi连接状态 */
    if (!wifi_is_connected()) {
        return;
    }

    /* 检查是否到达上报时间 */
    int64_t current_time = esp_timer_get_time() / 1000;  /* 当前时间（毫秒） */
    if ((current_time - s_last_wifi_location_report_time) >= s_wifi_location_report_interval) {
        ESP_LOGI(TAG, "Auto WiFi location report triggered");

        /* 执行WIFI定位上报 */
        int msg_id = wifi_location_report_once();
        if (msg_id == -1) {
            ESP_LOGW(TAG, "Auto WiFi location report failed");
        } else {
            ESP_LOGI(TAG, "Auto WiFi location report success, msg_id=%d", msg_id);
        }

        /* 更新上次上报时间 */
        s_last_wifi_location_report_time = current_time;
    }
}

/**
 * @brief 处理WiFi状态变化（内部函数）
 * @details 检查WiFi状态是否发生变化，如果是则调用回调函数
 */
static void process_wifi_state_change(void)
{
    if (s_wifi_state_changed && s_wifi_callback) {
        s_wifi_state_changed = false;

        /* 调用用户回调函数 */
        bool connected = (s_wifi_state == WIFI_STATE_CONNECTED);
        s_wifi_callback(connected);
    }
}

/**
 * @brief OneNET处理函数（需要在主循环中定期调用）
 * @details
 * 此函数处理WiFi状态检查、MQTT连接维护、自动数据上报等功能。
 * 必须在主循环中定期调用，建议调用间隔不超过100ms。
 */
void onenet_process(void)
{
    /* 处理WiFi状态变化 */
    process_wifi_state_change();

    /* 处理自动数据上报 */
    process_auto_report();

    /* 处理WIFI定位自动上报 */
    process_wifi_location_auto_report();

    /* 这里可以添加其他需要定期处理的功能 */
    /* 例如：MQTT连接状态检查、重连逻辑等 */
}

/* ==================== 便捷函数实现 ==================== */
/**
 * @brief WiFi和OneNET完整初始化（一键初始化）
 * @param wifi_callback WiFi状态变化回调函数
 * @param cmd_callback OneNET命令接收回调函数
 * @return esp_err_t ESP_OK表示成功，ESP_FAIL表示失败
 *
 * @details
 * 这是一个便捷函数，一次性完成WiFi和OneNET的初始化工作。
 *
 * 初始化流程：
 * 1. 初始化WiFi网络功能
 * 2. 初始化OneNET MQTT客户端
 * 3. 设置相关回调函数
 * 4. 记录初始化结果
 *
 * @note 调用此函数后，还需要调用wifi_connect()连接WiFi网络
 * @note 建议在WiFi连接成功后再调用onenet_mqtt_start()启动MQTT连接
 */
esp_err_t wifi_onenet_init(wifi_status_callback_t wifi_callback, onenet_cmd_callback_t cmd_callback)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "Starting WiFi and OneNET initialization...");

    /* 初始化WiFi功能 */
    ret = wifi_init(wifi_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "WiFi initialization failed");
        return ret;
    }

    /* 初始化OneNET MQTT客户端 */
    ret = onenet_mqtt_init(cmd_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "OneNET MQTT initialization failed");
        return ret;
    }

    ESP_LOGI(TAG, "WiFi and OneNET initialization completed successfully");
    return ESP_OK;
}

/* 注意：裸机版本不需要启动/停止任务函数，使用onenet_set_auto_report_interval()和onenet_process()代替 */

/**
 * @brief 获取WiFi和OneNET状态信息（调试用）
 *
 * @details
 * 打印当前WiFi连接状态和MQTT连接状态到串口，用于调试和状态监控。
 *
 * 输出信息包括：
 * - WiFi连接状态
 * - MQTT连接状态
 * - 自动上报任务状态
 * - 系统运行时间
 * - 内存使用情况
 */
void wifi_onenet_status_info(void)
{
    ESP_LOGI(TAG, "==================== WiFi & OneNET Status ====================");

    /* WiFi状态信息 */
    ESP_LOGI(TAG, "WiFi Status:");
    ESP_LOGI(TAG, "  - Connected: %s", wifi_is_connected() ? "YES" : "NO");
    ESP_LOGI(TAG, "  - SSID: %s", WIFI_SSID);
    ESP_LOGI(TAG, "  - Retry Count: %d/%d", s_retry_num, WIFI_MAXIMUM_RETRY);

    /* MQTT状态信息 */
    mqtt_client_state_t mqtt_state = onenet_mqtt_get_state();
    const char* state_str;
    switch (mqtt_state) {
        case MQTT_STATE_UNKNOWN:
            state_str = "UNKNOWN";
            break;
        case MQTT_STATE_INIT:
            state_str = "INITIALIZED";
            break;
        case MQTT_STATE_CONNECTED:
            state_str = "CONNECTED";
            break;
        case MQTT_STATE_DISCONNECTED:
            state_str = "DISCONNECTED";
            break;
        case MQTT_STATE_ERROR:
            state_str = "ERROR";
            break;
        default:
            state_str = "INVALID";
            break;
    }

    ESP_LOGI(TAG, "OneNET MQTT Status:");
    ESP_LOGI(TAG, "  - State: %s", state_str);
    ESP_LOGI(TAG, "  - Product ID: %s", ONENET_PRODUCT_ID);
    ESP_LOGI(TAG, "  - Device ID: %s", ONENET_DEVICE_ID);
    ESP_LOGI(TAG, "  - Host: %s:%d", ONENET_MQTT_HOST, ONENET_MQTT_PORT);

    /* 自动上报状态信息 */
    ESP_LOGI(TAG, "Auto Report Status:");
    ESP_LOGI(TAG, "  - Interval: %ld ms", s_auto_report_interval);
    ESP_LOGI(TAG, "  - Enabled: %s", (s_auto_report_interval > 0) ? "YES" : "NO");

    /* 系统信息 */
    ESP_LOGI(TAG, "System Info:");
    ESP_LOGI(TAG, "  - Free Heap: %" PRIu32 " bytes", esp_get_free_heap_size());
    ESP_LOGI(TAG, "  - Uptime: %lld ms", esp_timer_get_time() / 1000);

    ESP_LOGI(TAG, "============================================================");
}

/* ==================== 示例回调函数 ==================== */
/**
 * @brief 示例WiFi状态回调函数
 */
static void example_wifi_callback(bool connected)
{
    if (connected) {
        ESP_LOGI(TAG, "Example: WiFi connected! Starting OneNET MQTT...");

        /* WiFi连接成功后启动MQTT */
        esp_err_t mqtt_ret = onenet_mqtt_start();
        if (mqtt_ret == ESP_OK) {
            ESP_LOGI(TAG, "Example: OneNET MQTT started successfully");

            /* 设置自动数据上报间隔，每5秒上报一次 */
            onenet_set_auto_report_interval(5000);
        } else {
            ESP_LOGE(TAG, "Example: Failed to start OneNET MQTT");
        }
    } else {
        ESP_LOGI(TAG, "Example: WiFi disconnected! Stopping OneNET services...");

        /* WiFi断开时停止相关服务 */
        onenet_set_auto_report_interval(0);  /* 禁用自动上报 */
        onenet_mqtt_stop();
    }
}

/**
 * @brief 示例OneNET命令回调函数
 */
static void example_cmd_callback(const char* topic, const char* data, int data_len)
{
    ESP_LOGI(TAG, "Example: Received OneNET command");
    ESP_LOGI(TAG, "  Topic: %s", topic);
    ESP_LOGI(TAG, "  Data: %.*s", data_len, data);

    /* 这里可以添加具体的命令处理逻辑 */
    /* 例如：解析JSON命令，控制LED、继电器等设备 */
}

/**
 * @brief WiFi和OneNET完整示例初始化（裸机版本）
 * @return esp_err_t ESP_OK表示初始化成功，ESP_FAIL表示初始化失败
 *
 * @details
 * 这是一个完整的示例函数，演示了如何使用本模块的裸机版本。
 *
 * 示例流程：
 * 1. 初始化WiFi和OneNET（使用默认回调函数）
 * 2. 启动WiFi连接
 * 3. 设置自动数据上报间隔
 * 4. 打印状态信息
 *
 * @note 调用此函数后，需要在主循环中定期调用onenet_process()
 * @note 调用此函数前请确保已正确配置WiFi和OneNET参数
 */
esp_err_t wifi_onenet_example_init(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "Starting WiFi and OneNET example initialization (bare metal version)...");

    /* 1. 初始化WiFi和OneNET */
    ret = wifi_onenet_init(example_wifi_callback, example_cmd_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Example: WiFi and OneNET initialization failed");
        return ret;
    }

    /* 2. 启动WiFi连接（非阻塞） */
    ret = wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Example: WiFi connection start failed");
        return ret;
    }

    /* 3. 打印初始状态信息 */
    wifi_onenet_status_info();

    ESP_LOGI(TAG, "WiFi and OneNET example initialization completed successfully");
    ESP_LOGI(TAG, "The system will automatically:");
    ESP_LOGI(TAG, "  - Connect to WiFi: %s", WIFI_SSID);
    ESP_LOGI(TAG, "  - Connect to OneNET MQTT server");
    ESP_LOGI(TAG, "  - Start auto reporting every 5 seconds");
    ESP_LOGI(TAG, "  - Handle commands from OneNET platform");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "IMPORTANT: Call onenet_process() regularly in your main loop!");

    return ESP_OK;
}

/* ==================== WIFI定位功能实现 ==================== */

/**
 * @brief 执行WIFI热点扫描
 * @param ap_list 输出参数，存储扫描到的热点信息
 * @param max_ap 最大扫描热点数量
 * @param timeout_ms 扫描超时时间（毫秒）
 * @return int 实际扫描到的热点数量，失败时返回-1
 */
int wifi_scan_access_points(wifi_ap_info_t *ap_list, int max_ap, uint32_t timeout_ms)
{
    if (ap_list == NULL || max_ap <= 0) {
        ESP_LOGE(TAG, "Invalid parameters for WiFi scan");
        return -1;
    }

    ESP_LOGI(TAG, "Starting WiFi scan...");

    /* 配置扫描参数 */
    wifi_scan_config_t scan_config = {
        .ssid = NULL,           /* 扫描所有SSID */
        .bssid = NULL,          /* 扫描所有BSSID */
        .channel = 0,           /* 扫描所有信道 */
        .show_hidden = true,    /* 显示隐藏网络 */
        .scan_type = WIFI_SCAN_TYPE_ACTIVE,
        .scan_time = {
            .active = {
                .min = 100,     /* 每个信道最小扫描时间 */
                .max = 300      /* 每个信道最大扫描时间 */
            }
        }
    };

    /* 开始扫描 */
    esp_err_t ret = esp_wifi_scan_start(&scan_config, true);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi scan: %s", esp_err_to_name(ret));
        return -1;
    }

    /* 获取扫描结果数量 */
    uint16_t scan_count = 0;
    ret = esp_wifi_scan_get_ap_num(&scan_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get scan count: %s", esp_err_to_name(ret));
        return -1;
    }

    ESP_LOGI(TAG, "Found %d access points", scan_count);

    if (scan_count == 0) {
        return 0;
    }

    /* 限制扫描结果数量 */
    if (scan_count > max_ap) {
        scan_count = max_ap;
    }

    /* 分配临时缓冲区存储扫描结果 */
    wifi_ap_record_t *ap_records = malloc(scan_count * sizeof(wifi_ap_record_t));
    if (ap_records == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for scan results");
        return -1;
    }

    /* 获取扫描结果 */
    ret = esp_wifi_scan_get_ap_records(&scan_count, ap_records);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get scan results: %s", esp_err_to_name(ret));
        free(ap_records);
        return -1;
    }

    /* 转换扫描结果格式 */
    for (int i = 0; i < scan_count; i++) {
        /* 复制SSID */
        strncpy(ap_list[i].ssid, (char*)ap_records[i].ssid, sizeof(ap_list[i].ssid) - 1);
        ap_list[i].ssid[sizeof(ap_list[i].ssid) - 1] = '\0';

        /* 格式化BSSID为字符串 */
        snprintf(ap_list[i].bssid, sizeof(ap_list[i].bssid),
            "%02X:%02X:%02X:%02X:%02X:%02X",
            ap_records[i].bssid[0], ap_records[i].bssid[1], ap_records[i].bssid[2],
            ap_records[i].bssid[3], ap_records[i].bssid[4], ap_records[i].bssid[5]);

        /* 复制其他信息 */
        ap_list[i].rssi = ap_records[i].rssi;
        ap_list[i].channel = ap_records[i].primary;
        ap_list[i].authmode = ap_records[i].authmode;

        ESP_LOGD(TAG, "AP[%d]: %s (%s) RSSI:%d CH:%d",
            i, ap_list[i].ssid, ap_list[i].bssid, ap_list[i].rssi, ap_list[i].channel);
    }

    /* 释放临时缓冲区 */
    free(ap_records);

    ESP_LOGI(TAG, "WiFi scan completed, found %d access points", scan_count);
    return scan_count;
}

/**
 * @brief 获取当前连接的WIFI热点信息
 * @param connected_ap 输出参数，存储当前连接的热点信息
 * @return esp_err_t ESP_OK表示成功，ESP_FAIL表示失败
 */
esp_err_t wifi_get_connected_ap_info(wifi_ap_info_t *connected_ap)
{
    if (connected_ap == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: connected_ap is NULL");
        return ESP_FAIL;
    }

    /* 检查WiFi连接状态 */
    if (!wifi_is_connected()) {
        ESP_LOGW(TAG, "WiFi is not connected");
        return ESP_FAIL;
    }

    /* 获取当前连接的AP信息 */
    wifi_ap_record_t ap_info;
    esp_err_t ret = esp_wifi_sta_get_ap_info(&ap_info);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get connected AP info: %s", esp_err_to_name(ret));
        return ESP_FAIL;
    }

    /* 转换格式 */
    strncpy(connected_ap->ssid, (char*)ap_info.ssid, sizeof(connected_ap->ssid) - 1);
    connected_ap->ssid[sizeof(connected_ap->ssid) - 1] = '\0';

    snprintf(connected_ap->bssid, sizeof(connected_ap->bssid),
        "%02X:%02X:%02X:%02X:%02X:%02X",
        ap_info.bssid[0], ap_info.bssid[1], ap_info.bssid[2],
        ap_info.bssid[3], ap_info.bssid[4], ap_info.bssid[5]);

    connected_ap->rssi = ap_info.rssi;
    connected_ap->channel = ap_info.primary;
    connected_ap->authmode = ap_info.authmode;

    ESP_LOGD(TAG, "Connected AP: %s (%s) RSSI:%d CH:%d",
        connected_ap->ssid, connected_ap->bssid, connected_ap->rssi, connected_ap->channel);

    return ESP_OK;
}

/**
 * @brief 收集WIFI定位数据
 * @param location_data 输出参数，存储收集到的WIFI定位数据
 * @return esp_err_t ESP_OK表示成功，ESP_FAIL表示失败
 */
esp_err_t wifi_collect_location_data(wifi_location_data_t *location_data)
{
    if (location_data == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: location_data is NULL");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Collecting WiFi location data...");

    /* 清空数据结构 */
    memset(location_data, 0, sizeof(wifi_location_data_t));

    /* 1. 获取设备MAC地址（作为smac） */
    uint8_t mac[6];
    esp_err_t ret = esp_wifi_get_mac(WIFI_IF_STA, mac);
    if (ret == ESP_OK) {
        snprintf(location_data->smac, sizeof(location_data->smac),
            "%02X:%02X:%02X:%02X:%02X:%02X",
            mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        ESP_LOGD(TAG, "Device MAC: %s", location_data->smac);
    } else {
        ESP_LOGW(TAG, "Failed to get device MAC address");
        strcpy(location_data->smac, "00:00:00:00:00:00");
    }

    /* 2. 获取网关IP地址（作为serverip） */
    esp_netif_t *netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    if (netif != NULL) {
        esp_netif_ip_info_t ip_info;
        ret = esp_netif_get_ip_info(netif, &ip_info);
        if (ret == ESP_OK) {
            snprintf(location_data->serverip, sizeof(location_data->serverip),
                IPSTR, IP2STR(&ip_info.gw));
            ESP_LOGD(TAG, "Gateway IP: %s", location_data->serverip);
        } else {
            ESP_LOGW(TAG, "Failed to get gateway IP");
            strcpy(location_data->serverip, "0.0.0.0");
        }
    } else {
        ESP_LOGW(TAG, "Failed to get network interface");
        strcpy(location_data->serverip, "0.0.0.0");
    }

    /* 3. 设置IMSI（ESP32没有真实IMSI，使用设备ID代替） */
    uint64_t chip_id = 0;
    for (int i = 0; i < 17; i = i + 8) {
        chip_id |= ((uint64_t)esp_random() << i);
    }
    snprintf(location_data->imsi, sizeof(location_data->imsi), "%llu", chip_id);
    ESP_LOGD(TAG, "Device IMSI: %s", location_data->imsi);

    /* 4. 设置IDFA（使用芯片ID代替） */
    snprintf(location_data->idfa, sizeof(location_data->idfa),
        "%08X-%04X-%04X-%04X-%08X%04X",
        (uint32_t)(chip_id >> 32), (uint16_t)(chip_id >> 16),
        (uint16_t)chip_id, (uint16_t)esp_random(),
        (uint32_t)esp_random(), (uint16_t)esp_random());
    ESP_LOGD(TAG, "Device IDFA: %s", location_data->idfa);

    /* 5. 获取当前连接的热点信息（mmac） */
    wifi_ap_info_t connected_ap;
    if (wifi_get_connected_ap_info(&connected_ap) == ESP_OK) {
        snprintf(location_data->mmac, sizeof(location_data->mmac),
            "%s,%d", connected_ap.bssid, connected_ap.rssi);
        ESP_LOGD(TAG, "Connected AP: %s", location_data->mmac);
    } else {
        ESP_LOGW(TAG, "Failed to get connected AP info");
        strcpy(location_data->mmac, "");
    }

    /* 6. 扫描周围热点（macs） */
    wifi_ap_info_t scan_results[WIFI_SCAN_MAX_AP];
    int scan_count = wifi_scan_access_points(scan_results, WIFI_SCAN_MAX_AP, WIFI_SCAN_TIMEOUT_MS);

    if (scan_count > 0) {
        /* 构造macs字符串：MAC,RSSI|MAC,RSSI|... */
        char *macs_ptr = location_data->macs;
        size_t remaining = sizeof(location_data->macs) - 1;

        for (int i = 0; i < scan_count && remaining > 0; i++) {
            int written = snprintf(macs_ptr, remaining, "%s%s,%d",
                (i > 0) ? "|" : "", scan_results[i].bssid, scan_results[i].rssi);

            if (written > 0 && written < remaining) {
                macs_ptr += written;
                remaining -= written;
            } else {
                break;  /* 缓冲区不足 */
            }
        }

        ESP_LOGD(TAG, "Scanned APs: %s", location_data->macs);
    } else {
        ESP_LOGW(TAG, "No access points found in scan");
        strcpy(location_data->macs, "");
    }

    ESP_LOGI(TAG, "WiFi location data collection completed");
    return ESP_OK;
}

/**
 * @brief 上报WIFI定位数据到OneNET平台
 * @param location_data WIFI定位数据结构指针
 * @return int 消息ID，成功时返回正数，失败时返回-1
 */
int onenet_report_wifi_location(const wifi_location_data_t *location_data)
{
    /* 参数有效性检查 */
    if (s_mqtt_client == NULL || s_mqtt_state != MQTT_STATE_CONNECTED) {
        ESP_LOGW(TAG, "MQTT client not connected, cannot report WiFi location");
        return -1;
    }

    if (location_data == NULL) {
        ESP_LOGE(TAG, "Invalid parameter: location_data is NULL");
        return -1;
    }

    ESP_LOGI(TAG, "Reporting WiFi location data to OneNET...");

    /* 构造OneNET WIFI定位数据格式 */
    char params_content[1024];
    snprintf(params_content, sizeof(params_content),
        "\"$OneNET_LBS_WIFI\":{\"value\":{"
        "\"imsi\":\"%s\","
        "\"serverip\":\"%s\","
        "\"macs\":\"%s\","
        "\"mmac\":\"%s\","
        "\"smac\":\"%s\","
        "\"idfa\":\"%s\""
        "}}",
        location_data->imsi,
        location_data->serverip,
        location_data->macs,
        location_data->mmac,
        location_data->smac,
        location_data->idfa
    );

    ESP_LOGD(TAG, "WiFi location params: %s", params_content);

    /* 调用通用上报函数 */
    int msg_id = onenet_report_properties(params_content);
    if (msg_id != -1) {
        ESP_LOGI(TAG, "WiFi location data reported successfully, msg_id=%d", msg_id);
    } else {
        ESP_LOGE(TAG, "Failed to report WiFi location data");
    }

    return msg_id;
}

/**
 * @brief 执行完整的WIFI定位流程（便捷函数）
 * @return int 消息ID，成功时返回正数，失败时返回-1
 */
int wifi_location_report_once(void)
{
    ESP_LOGI(TAG, "Starting WiFi location report process...");

    /* 收集WIFI定位数据 */
    wifi_location_data_t location_data;
    esp_err_t ret = wifi_collect_location_data(&location_data);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to collect WiFi location data");
        return -1;
    }

    /* 上报数据到OneNET平台 */
    int msg_id = onenet_report_wifi_location(&location_data);
    if (msg_id != -1) {
        ESP_LOGI(TAG, "WiFi location report completed successfully");
    } else {
        ESP_LOGE(TAG, "WiFi location report failed");
    }

    return msg_id;
}

/**
 * @brief 设置WIFI定位自动上报间隔
 * @param interval_ms 自动上报间隔时间（毫秒），0表示禁用自动上报
 * @return esp_err_t ESP_OK表示成功
 */
esp_err_t wifi_location_set_auto_report_interval(uint32_t interval_ms)
{
    s_wifi_location_report_interval = interval_ms;
    s_last_wifi_location_report_time = esp_timer_get_time() / 1000;  /* 重置时间戳 */

    if (interval_ms > 0) {
        ESP_LOGI(TAG, "WiFi location auto report enabled, interval: %lu ms", interval_ms);
    } else {
        ESP_LOGI(TAG, "WiFi location auto report disabled");
    }

    return ESP_OK;
}
