
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      clang: error: ld command failed due to signal (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is Clang, found in:
        C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/3.30.2/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/3.30.2/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-b5qi33"
      binary: "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-b5qi33"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-b5qi33'
        
        Run Build Command(s): D:/ESP-IDF/Espressif/tools/ninja/1.12.1/ninja.exe -v cmTC_89551
        [1/2] D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe   -v -MD -MT CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -MF CMakeFiles\\cmTC_89551.dir\\CMakeCCompilerABI.c.obj.d -o CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -c D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        clang version 18.1.2 (https://github.com/espressif/llvm-project.git esp-18.1.2_20240912)
        Target: riscv32-esp-unknown-elf
        Thread model: posix
        InstalledDir: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin
         (in-process)
         "D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang.exe" -cc1 -triple riscv32-esp-unknown-elf -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model static -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -nostdsysteminc -target-cpu generic-rv32 -target-feature +m -target-feature +a -target-feature +c -target-feature -d -target-feature -e -target-feature -f -target-feature -h -target-feature -smaia -target-feature -smepmp -target-feature -ssaia -target-feature -svinval -target-feature -svnapot -target-feature -svpbmt -target-feature -v -target-feature -xcvalu -target-feature -xcvbi -target-feature -xcvbitmanip -target-feature -xcvelw -target-feature -xcvmac -target-feature -xcvmem -target-feature -xcvsimd -target-feature -xesppie -target-feature -xsfvcp -target-feature -xsfvfnrclipxfqf -target-feature -xsfvfwmaccqqq -target-feature -xsfvqmaccdod -target-feature -xsfvqmaccqoq -target-feature -xtheadba -target-feature -xtheadbb -target-feature -xtheadbs -target-feature -xtheadcmo -target-feature -xtheadcondmov -target-feature -xtheadfmemidx -target-feature -xtheadmac -target-feature -xtheadmemidx -target-feature -xtheadmempair -target-feature -xtheadsync -target-feature -xtheadvdot -target-feature -xventanacondops -target-feature -za128rs -target-feature -za64rs -target-feature -zawrs -target-feature -zba -target-feature -zbb -target-feature -zbc -target-feature -zbkb -target-feature -zbkc -target-feature -zbkx -target-feature -zbs -target-feature -zca -target-feature -zcb -target-feature -zcd -target-feature -zce -target-feature -zcf -target-feature -zcmp -target-feature -zcmt -target-feature -zdinx -target-feature -zfa -target-feature -zfh -target-feature -zfhmin -target-feature -zfinx -target-feature -zhinx -target-feature -zhinxmin -target-feature -zic64b -target-feature -zicbom -target-feature -zicbop -target-feature -zicboz -target-feature -ziccamoa -target-feature -ziccif -target-feature -zicclsm -target-feature -ziccrse -target-feature -zicntr -target-feature -zicond -target-feature -zicsr -target-feature -zifencei -target-feature -zihintntl -target-feature -zihintpause -target-feature -zihpm -target-feature -zk -target-feature -zkn -target-feature -zknd -target-feature -zkne -target-feature -zknh -target-feature -zkr -target-feature -zks -target-feature -zksed -target-feature -zksh -target-feature -zkt -target-feature -zmmul -target-feature -zvbb -target-feature -zvbc -target-feature -zve32f -target-feature -zve32x -target-feature -zve64d -target-feature -zve64f -target-feature -zve64x -target-feature -zvfh -target-feature -zvfhmin -target-feature -zvkb -target-feature -zvkg -target-feature -zvkn -target-feature -zvknc -target-feature -zvkned -target-feature -zvkng -target-feature -zvknha -target-feature -zvknhb -target-feature -zvks -target-feature -zvksc -target-feature -zvksed -target-feature -zvksg -target-feature -zvksh -target-feature -zvkt -target-feature -zvl1024b -target-feature -zvl128b -target-feature -zvl16384b -target-feature -zvl2048b -target-feature -zvl256b -target-feature -zvl32768b -target-feature -zvl32b -target-feature -zvl4096b -target-feature -zvl512b -target-feature -zvl64b -target-feature -zvl65536b -target-feature -zvl8192b -target-feature -experimental-zacas -target-feature -experimental-zcmop -target-feature -experimental-zfbfmin -target-feature -experimental-zicfilp -target-feature -experimental-zicfiss -target-feature -experimental-zimop -target-feature -experimental-ztso -target-feature -experimental-zvfbfmin -target-feature -experimental-zvfbfwma -target-feature +relax -target-abi ilp32 -msmall-data-limit 8 -debugger-tuning=gdb -fdebug-compilation-dir=C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-b5qi33 -v -fcoverage-compilation-dir=C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-b5qi33 -resource-dir D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/lib/clang/18 -dependency-file "CMakeFiles\\\\cmTC_89551.dir\\\\CMakeCCompilerABI.c.obj.d" -MT CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -sys-header-deps -internal-isystem D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/lib/clang/18/include -internal-isystem D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/../lib/clang-runtimes/riscv32-esp-unknown-elf/rv32imac-zicsr-zifencei_ilp32/include -ferror-limit 19 -fno-signed-char -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -faddrsig -o CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -x c D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        clang -cc1 version 18.1.2 based upon LLVM 18.1.2 default target riscv32-esp-elf\x0d
        ignoring duplicate directory "D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/lib/clang/18/include"\x0d
        #include "..." search starts here:\x0d
        #include <...> search starts here:\x0d
         D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/lib/clang/18/include\x0d
         D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/../lib/clang-runtimes/riscv32-esp-unknown-elf/rv32imac-zicsr-zifencei_ilp32/include\x0d
        End of search list.\x0d
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe  -v -Wl,-v CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -o cmTC_89551.exe -Wl,--out-implib,libcmTC_89551.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        FAILED: cmTC_89551.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe  -v -Wl,-v CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj -o cmTC_89551.exe -Wl,--out-implib,libcmTC_89551.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        clang version 18.1.2 (https://github.com/espressif/llvm-project.git esp-18.1.2_20240912)
        Target: riscv32-esp-unknown-elf
        Thread model: posix
        InstalledDir: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin
         "D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/ld.lld" -m elf32lriscv -o cmTC_89551.exe -X D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/../lib/clang-runtimes/riscv32-esp-unknown-elf/rv32imac-zicsr-zifencei_ilp32/lib/crt0.o -v CMakeFiles/cmTC_89551.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_89551.dll.a --major-image-version 0 --minor-image-version 0 -LD:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/../lib/clang-runtimes/riscv32-esp-unknown-elf/rv32imac-zicsr-zifencei_ilp32/lib -lm --start-group -lc -lgloss -lnosys --end-group -lclang_rt.builtins\x0d
        ld.lld: error: unknown argument '--major-image-version'\x0d
        ld.lld: error: unknown argument '--minor-image-version'\x0d
        LLD 18.1.2 (compatible with GNU linkers)
        ld.lld: error: cannot open 0: No such file or directory\x0d
        ld.lld: error: cannot open 0: No such file or directory\x0d
        clang: error: ld command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/ESP-IDF/Espressif/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt"
    checks:
      - "Check for working C compiler: D:/ESP-IDF/Espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clang.exe"
    directories:
      source: "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-aas23g"
      binary: "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-aas23g"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_C_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-aas23g'
        
        Run Build Command(s): D:/ESP-IDF/Espressif/tools/ninja/1.12.1/ninja.exe -v cmTC_ccb07
        [1/2] D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe    -MD -MT CMakeFiles/cmTC_ccb07.dir/testCCompiler.c.obj -MF CMakeFiles\\cmTC_ccb07.dir\\testCCompiler.c.obj.d -o CMakeFiles/cmTC_ccb07.dir/testCCompiler.c.obj -c C:/Users/<USER>/Desktop/ESP32/Code/5.Location/build/CMakeFiles/CMakeScratch/TryCompile-aas23g/testCCompiler.c
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe   CMakeFiles/cmTC_ccb07.dir/testCCompiler.c.obj -o cmTC_ccb07.exe -Wl,--out-implib,libcmTC_ccb07.dll.a -Wl,--major-image-version,0,--minor-image-version,0  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 && cd ."
        FAILED: cmTC_ccb07.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clang.exe   CMakeFiles/cmTC_ccb07.dir/testCCompiler.c.obj -o cmTC_ccb07.exe -Wl,--out-implib,libcmTC_ccb07.dll.a -Wl,--major-image-version,0,--minor-image-version,0  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 && cd ."
        ld.lld: error: unknown argument '--major-image-version'
        ld.lld: error: unknown argument '--minor-image-version'
        ld.lld: error: cannot open 0: No such file or directory
        ld.lld: error: cannot open 0: No such file or directory
        ld.lld: error: unable to find library -lkernel32
        ld.lld: error: unable to find library -luser32
        ld.lld: error: unable to find library -lgdi32
        ld.lld: error: unable to find library -lwinspool
        ld.lld: error: unable to find library -lshell32
        ld.lld: error: unable to find library -lole32
        ld.lld: error: unable to find library -loleaut32
        ld.lld: error: unable to find library -luuid
        ld.lld: error: unable to find library -lcomdlg32\x0d
        ld.lld: error: unable to find library -ladvapi32\x0d
        clang: error: ld command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
